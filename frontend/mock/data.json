json
<!-- x<PERSON><PERSON>_subway_commercial_management/frontend/mock/data.json -->
{
  "commercialSpots": [
    {
      "id": "XM-2025-001",
      "name": "镇海路站1号点位",
      "line": "1号线",
      "station": "镇海路站",
      "area": 45,
      "type": "餐饮",
      "status": "leased",
      "tenant": "星巴克咖啡",
      "rent": 45000,
      "contractStart": "2023-01-01",
      "contractEnd": "2025-12-31",
      "paymentStatus": "paid",
      "images": [
        "https://picsum.photos/400/300?random=101",
        "https://picsum.photos/400/300?random=102"
      ],
      "coordinates": [24.4567, 118.0789]
    },
    {
      "id": "XM-2025-002",
      "name": "中山公园站2号点位",
      "line": "1号线",
      "station": "中山公园站",
      "area": 32,
      "type": "零售",
      "status": "vacant",
      "tenant": null,
      "rent": 32000,
      "contractStart": null,
      "contractEnd": null,
      "paymentStatus": null,
      "images": [
        "https://picsum.photos/400/300?random=103",
        "https://picsum.photos/400/300?random=104"
      ],
      "coordinates": [24.4589, 118.0821]
    },
    {
      "id": "XM-2025-003",
      "name": "五缘湾站3号点位",
      "line": "2号线",
      "station": "五缘湾站",
      "area": 60,
      "type": "餐饮",
      "status": "leased",
      "tenant": "肯德基",
      "rent": 55000,
      "contractStart": "2023-03-15",
      "contractEnd": "2025-09-14",
      "paymentStatus": "paid",
      "images": [
        "https://picsum.photos/400/300?random=105",
        "https://picsum.photos/400/300?random=106"
      ],
      "coordinates": [24.5321, 118.1567]
    }
  ],
  "tenants": [
    {
      "id": "T-2023-001",
      "name": "星巴克咖啡",
      "contact": "张经理",
      "phone": "***********",
      "email": "<EMAIL>",
      "businessType": "餐饮",
      "contractIds": ["XM-HT-2023-001"],
      "rating": 4.5,
      "notes": "长期合作客户",
      "logo": "https://picsum.photos/200/200?random=201"
    },
    {
      "id": "T-2023-002",
      "name": "肯德基",
      "contact": "李店长",
      "phone": "***********",
      "email": "<EMAIL>",
      "businessType": "快餐",
      "contractIds": ["XM-HT-2023-002"],
      "rating": 4.2,
      "notes": "全国连锁品牌",
      "logo": "https://picsum.photos/200/200?random=202"
    },
    {
      "id": "T-2023-003",
      "name": "周黑鸭",
      "contact": "王经理",
      "phone": "***********",
      "email": "<EMAIL>",
      "businessType": "食品零售",
      "contractIds": ["XM-HT-2023-003"],
      "rating": 4.0,
      "notes": "新签约客户",
      "logo": "https://picsum.photos/200/200?random=203"
    }
  ],
  "contracts": [
    {
      "id": "XM-HT-2023-001",
      "spotId": "XM-2025-001",
      "tenantId": "T-2023-001",
      "startDate": "2023-01-01",
      "endDate": "2025-12-31",
      "monthlyRent": 45000,
      "deposit": 90000,
      "paymentDay": 15,
      "terms": "3年合同，每年租金递增5%",
      "status": "active",
      "attachments": [],
      "paymentHistory": [
        {
          "date": "2025-07-15",
          "amount": 45000,
          "status": "paid"
        }
      ]
    },
    {
      "id": "XM-HT-2023-002",
      "spotId": "XM-2025-003",
      "tenantId": "T-2023-002",
      "startDate": "2023-03-15",
      "endDate": "2025-09-14",
      "monthlyRent": 55000,
      "deposit": 110000,
      "paymentDay": 10,
      "terms": "2.5年合同，固定租金",
      "status": "active",
      "attachments": [],
      "paymentHistory": [
        {
          "date": "2025-07-10",
          "amount": 55000,
          "status": "paid"
        }
      ]
    },
    {
      "id": "XM-HT-2023-003",
      "spotId": "XM-2025-004",
      "tenantId": "T-2023-003",
      "startDate": "2023-05-20",
      "endDate": "2025-05-19",
      "monthlyRent": 38000,
      "deposit": 76000,
      "paymentDay": 20,
      "terms": "2年合同，无递增条款",
      "status": "active",
      "attachments": [],
      "paymentHistory": [
        {
          "date": "2025-07-20",
          "amount": 38000,
          "status": "pending"
        }
      ]
    }
  ],
  "payments": [
    {
      "id": "PY-202307-001",
      "contractId": "XM-HT-2023-001",
      "amount": 45000,
      "date": "2025-07-15",
      "status": "paid",
      "method": "bank",
      "invoiceNo": "INV202307001"
    },
    {
      "id": "PY-202307-002",
      "contractId": "XM-HT-2023-002",
      "amount": 55000,
      "date": "2025-07-10",
      "status": "paid",
      "method": "bank",
      "invoiceNo": "INV202307002"
    },
    {
      "id": "PY-202307-003",
      "contractId": "XM-HT-2023-003",
      "amount": 38000,
      "date": "2025-07-20",
      "status": "pending",
      "method": null,
      "invoiceNo": null
    }
  ],
  "financialStats": {
    "2025": {
      "monthlyIncome": [1200000, 1180000, 1250000, 1300000, 1280000, 1350000, 1400000, 0, 0, 0, 0, 0],
      "incomeByType": {
        "餐饮": 9000000,
        "零售": 3000000,
        "服务": 2000000,
        "广告": 1000000,
        "其他": 360000
      },
      "totalIncome": ********
    }
  },
  "notifications": [
    {
      "id": 1,
      "title": "系统维护通知",
      "content": "系统将于2025-08-01 02:00-06:00进行维护升级",
      "date": "2025-07-20",
      "type": "system",
      "read": false
    },
    {
      "id": 2,
      "title": "合同即将到期",
      "content": "XM-HT-2023-003 周黑鸭合同将于30天后到期",
      "date": "2025-07-15",
      "type": "contract",
      "read": false
    }
  ]
}
