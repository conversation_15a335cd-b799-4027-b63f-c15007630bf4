
<!-- xiamen_subway_commercial_management/frontend/admin.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>后台管理 - 厦门地铁商业点位管理系统</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }
    .nav-item {
      transition: all 0.3s ease;
    }
    .nav-item:hover {
      transform: translateY(-2px);
    }
    .card {
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    #particles-js {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    .sidebar {
      transition: all 0.3s ease;
    }
    .sidebar-item {
      transition: all 0.2s ease;
    }
    .sidebar-item:hover {
      background-color: rgba(59, 130, 246, 0.1);
    }
    .user-menu {
      position: relative;
    }
    .dropdown-menu {
      position: absolute;
      right: 0;
      top: 100%;
      margin-top: 0.5rem;
      width: 200px;
      background-color: white;
      border-radius: 0.375rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      z-index: 50;
    }
    .dropdown-menu.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }
    .dropdown-item {
      padding: 0.5rem 1rem;
      color: #4a5568;
      transition: all 0.2s;
    }
    .dropdown-item:hover {
      background-color: #f7fafc;
      color: #4299e1;
    }
  </style>
</head>
<body class="antialiased">
  <div id="particles-js"></div>
  
  <div class="flex h-screen">
    <!-- 侧边栏 -->
    <div class="sidebar w-64 bg-white shadow-lg">
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4" />
          </svg>
          <h1 class="text-xl font-bold text-gray-800">后台管理系统</h1>
        </div>
      </div>
      <nav class="p-4">
        <div class="space-y-1">
          <a href="#" class="sidebar-item flex items-center px-4 py-2 text-sm font-medium text-gray-900 rounded-md bg-blue-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            控制面板
          </a>
          <a href="#users" class="sidebar-item flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-blue-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 01112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            用户管理
          </a>
          <a href="#settings" class="sidebar-item flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-blue-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            系统设置
          </a>
          <a href="#logs" class="sidebar-item flex items-center px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-blue-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            操作日志
          </a>
        </div>
      </nav>
    </div>

    <!-- 主内容区 -->
    <div class="flex-1 overflow-auto">
      <header class="bg-white shadow-sm">
        <div class="flex justify-between items-center px-6 py-4">
          <h2 class="text-xl font-semibold text-gray-800">后台管理控制台</h2>
          <div class="flex items-center space-x-4">
            <div class="relative">
              <button class="p-2 rounded-full hover:bg-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              </button>
              <span class="absolute top-0 right-0 h-3 w-3 rounded-full bg-red-500"></span>
            </div>
            <div class="user-menu flex items-center space-x-2 cursor-pointer">
              <div class="flex items-center space-x-2" id="user-menu-button">
                <img src="https://picsum.photos/200/200?random=1" alt="用户头像" class="h-8 w-8 rounded-full">
                <span class="font-medium">超级管理员</span>
              </div>
              <div class="dropdown-menu" id="user-dropdown">
                <div class="py-1">
                  <a href="#" class="dropdown-item flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    个人资料
                  </a>
                  <a href="#" class="dropdown-item flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    系统设置
                  </a>
                  <div class="border-t border-gray-200 my-1"></div>
                  <a href="login.html" class="dropdown-item flex items-center text-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    退出登录
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容保持不变 -->
      <main class="p-6">
        <!-- 用户管理 -->
        <div id="users" class="mb-8">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">用户管理</h3>
            <button onclick="showAddUserModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              添加用户
            </button>
          </div>
          <div class="card bg-white rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邮箱</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="users-table">
                  <!-- 用户数据将通过JavaScript动态加载 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 系统设置 -->
        <div id="settings" class="mb-8">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">系统设置</h3>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="card bg-white rounded-lg p-6">
              <h4 class="text-lg font-semibold text-gray-800 mb-4">系统参数</h4>
              <form id="system-settings-form">
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-1">系统名称</label>
                  <input type="text" name="system_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-1">默认时区</label>
                  <select name="timezone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="Asia/Shanghai">Asia/Shanghai</option>
                    <option value="UTC">UTC</option>
                  </select>
                </div>
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-1">数据备份频率</label>
                  <select name="backup_frequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="daily">每日</option>
                    <option value="weekly">每周</option>
                    <option value="monthly">每月</option>
                  </select>
                </div>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">保存设置</button>
                </form>
            </div>
            <div class="card bg-white rounded-lg p-6">
              <h4 class="text-lg font-semibold text-gray-800 mb-4">数据管理</h4>
              <div class="space-y-4">
                <button onclick="backupData()" class="w-full px-4 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition flex items-center justify-between">
                  <span>备份数据</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                  </svg>
                </button>
                <button onclick="showRestoreModal()" class="w-full px-4 py-2 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 transition flex items-center justify-between">
                  <span>恢复数据</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
                <button onclick="showResetModal()" class="w-full px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition flex items-center justify-between">
                  <span>重置系统</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作日志 -->
        <div id="logs">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">操作日志</h3>
            <div class="flex space-x-2">
              <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition">导出Excel</button>
              <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition">清空日志</button>
            </div>
          </div>
          <div class="card bg-white rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作者</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">详情</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="logs-table">
                  <!-- 日志数据将通过JavaScript动态加载 -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 添加用户模态框 -->
  <div id="add-user-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">添加新用户</h3>
          <form id="add-user-form">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <input type="text" name="username" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
              <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
              <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">角色</label>
              <select name="role" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="admin">管理员</option>
                <option value="operator">操作员</option>
                <option value="viewer">查看者</option>
              </select>
            </div>
            <div class="flex justify-end space-x-3">
              <button type="button" onclick="hideAddUserModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">取消</button>
              <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">保存</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- 恢复数据模态框 -->
  <div id="restore-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">恢复数据</h3>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">选择备份文件</label>
            <input type="file" id="backup-file" accept=".json" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>
          <div class="flex justify-end space-x-3">
            <button type="button" onclick="hideRestoreModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition">取消</button>
            <button type="button" onclick="restoreData()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">恢复</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 重置系统确认模态框 -->
  <div id="reset-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 class="text-lg leading-6 font-medium text-gray-900">重置系统确认</h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">此操作将清除所有数据并恢复系统到初始状态。此操作不可撤销，请谨慎操作。</p>
              </div>
            </div>
          </div>
          <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <button type="button" onclick="resetSystem()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">确认重置</button>
            <button type="button" onclick="hideResetModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm">取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
  <script>
    // 粒子效果初始化
    document.addEventListener('DOMContentLoaded', function() {
      particlesJS('particles-js', {
        particles: {
          number: { value: 80, density: { enable: true, value_area: 800 } },
          color: { value: "#3b82f6" },
          shape: { type: "circle" },
          opacity: { value: 0.5, random: true },
          size: { value: 3, random: true },
          line_linked: { enable: true, distance: 150, color: "#3b82f6", opacity: 0.4, width: 1 },
          move: { enable: true, speed: 2, direction: "none", random: true, straight: false, out_mode: "out" }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: { enable: true, mode: "grab" },
            onclick: { enable: true, mode: "push" }
          }
        }
      });

      // 用户菜单交互
      const userMenuButton = document.getElementById('user-menu-button');
      const userDropdown = document.getElementById('user-dropdown');

      userMenuButton.addEventListener('click', function(e) {
        e.stopPropagation();
        userDropdown.classList.toggle('show');
      });

      // 点击页面其他区域关闭下拉菜单
      document.addEventListener('click', function() {
        userDropdown.classList.remove('show');
      });

      // 阻止下拉菜单内部的点击事件冒泡
      userDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
      });

      // 加载用户数据
      loadUsers();
      // 加载日志数据
      loadLogs();
      // 加载系统设置
      loadSystemSettings();
    });

    // 用户管理相关函数
    function loadUsers() {
      const users = JSON.parse(localStorage.getItem('admin_users')) || [
        { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin', status: 'active', avatar: 'https://picsum.photos/200/200?random=1' },
        { id: 2, username: 'operator1', email: '<EMAIL>', role: 'operator', status: 'active', avatar: 'https://picsum.photos/200/200?random=2' },
        { id: 3, username: 'viewer1', email: '<EMAIL>', role: 'viewer', status: 'inactive', avatar: 'https://picsum.photos/200/200?random=3' }
      ];
      
      const usersTable = document.getElementById('users-table');
      usersTable.innerHTML = '';
      
      users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-10 w-10">
                <img class="h-10 w-10 rounded-full" src="${user.avatar}" alt="">
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">${user.username}</div>
                <div class="text-sm text-gray-500">${user.email}</div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            ${user.role === 'admin' ? '管理员' : user.role === 'operator' ? '操作员' : '查看者'}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${user.email}</td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
              ${user.status === 'active' ? '活跃' : '禁用'}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            <button onclick="editUser(${user.id})" class="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
            <button onclick="toggleUserStatus(${user.id})" class="${user.status === 'active' ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'}">
              ${user.status === 'active' ? '禁用' : '激活'}
            </button>
          </td>
        `;
        usersTable.appendChild(row);
      });
    }

    // 日志管理相关函数
    function loadLogs() {
      const logs = JSON.parse(localStorage.getItem('admin_logs')) || [
        { id: 1, timestamp: '2025-07-22 10:30:45', operator: 'admin', action: '登录系统', ip: '*************' },
        { id: 2, timestamp: '2025-07-22 09:15:22', operator: 'operator1', action: '添加点位', details: 'XM-2025-004', ip: '*************' },
        { id: 3, timestamp: '2025-07-21 16:45:18', operator: 'admin', action: '修改合同', details: 'XM-HT-2023-001', ip: '*************' }
      ];
      
      const logsTable = document.getElementById('logs-table');
      logsTable.innerHTML = '';
      
      logs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.timestamp}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.operator}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.action}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.details || '-'}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${log.ip}</td>
        `;
        logsTable.appendChild(row);
      });
    }

    // 系统设置相关函数
    function loadSystemSettings() {
      const settings = JSON.parse(localStorage.getItem('admin_settings')) || {
        system_name: '厦门地铁商业点位管理系统',
        timezone: 'Asia/Shanghai',
        backup_frequency: 'daily'
      };
      
      const form = document.getElementById('system-settings-form');
      form.system_name.value = settings.system_name;
      form.timezone.value = settings.timezone;
      form.backup_frequency.value = settings.backup_frequency;
      
      // 保存设置表单提交
      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const newSettings = {
          system_name: form.system_name.value,
          timezone: form.timezone.value,
          backup_frequency: form.backup_frequency.value
        };
        
        localStorage.setItem('admin_settings', JSON.stringify(newSettings));
        alert('系统设置已保存');
        
        // 记录日志
        addLog('admin', '修改系统设置');
      });
    }

    // 添加用户相关函数
    function showAddUserModal() {
      document.getElementById('add-user-modal').classList.remove('hidden');
    }

    function hideAddUserModal() {
      document.getElementById('add-user-modal').classList.add('hidden');
    }

    document.getElementById('add-user-form').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const form = e.target;
      const users = JSON.parse(localStorage.getItem('admin_users')) || [];
      
      const newUser = {
        id: users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1,
        username: form.username.value,
        email: form.email.value,
        role: form.role.value,
        status: 'active',
        avatar: `https://picsum.photos/200/200?random=${Math.floor(Math.random() * 1000)}`
      };
      
      users.push(newUser);
      localStorage.setItem('admin_users', JSON.stringify(users));
      
      hideAddUserModal();
      loadUsers();
      addLog('admin', `添加用户: ${newUser.username}`);
      
      form.reset();
    });

    // 用户状态切换
    function toggleUserStatus(userId) {
      const users = JSON.parse(localStorage.getItem('admin_users')) || [];
      const userIndex = users.findIndex(u => u.id === userId);
      
      if (userIndex !== -1) {
        users[userIndex].status = users[userIndex].status === 'active' ? 'inactive' : 'active';
        localStorage.setItem('admin_users', JSON.stringify(users));
        loadUsers();
        addLog('admin', `修改用户状态: ${users[userIndex].username}`);
      }
    }

    // 数据备份与恢复
    function backupData() {
      const data = {
        users: JSON.parse(localStorage.getItem('admin_users')) || [],
        settings: JSON.parse(localStorage.getItem('admin_settings')) || {},
        logs: JSON.parse(localStorage.getItem('admin_logs')) || []
      };
      
      const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `xiamen_metro_admin_backup_${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      addLog('admin', '备份系统数据');
    }

    function showRestoreModal() {
      document.getElementById('restore-modal').classList.remove('hidden');
    }

    function hideRestoreModal() {
      document.getElementById('restore-modal').classList.add('hidden');
    }

    function restoreData() {
      const fileInput = document.getElementById('backup-file');
      const file = fileInput.files[0];
      
      if (!file) {
        alert('请选择备份文件');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const data = JSON.parse(e.target.result);
          
          if (data.users) localStorage.setItem('admin_users', JSON.stringify(data.users));
          if (data.settings) localStorage.setItem('admin_settings', JSON.stringify(data.settings));
          if (data.logs) localStorage.setItem('admin_logs', JSON.stringify(data.logs));
          
          hideRestoreModal();
          loadUsers();
          loadLogs();
          loadSystemSettings();
          
          addLog('admin', '恢复系统数据');
          alert('数据恢复成功');
        } catch (error) {
          alert('恢复失败: 文件格式不正确');
        }
      };
      reader.readAsText(file);
    }

    // 系统重置
    function showResetModal() {
      document.getElementById('reset-modal').classList.remove('hidden');
    }

    function hideResetModal() {
      document.getElementById('reset-modal').classList.add('hidden');
    }

    function resetSystem() {
      localStorage.removeItem('admin_users');
      localStorage.removeItem('admin_settings');
      localStorage.removeItem('admin_logs');
      
      hideResetModal();
      loadUsers();
      loadLogs();
      loadSystemSettings();
      
      addLog('admin', '重置系统');
    }

    // 日志记录
    function addLog(operator, action, details) {
      const logs = JSON.parse(localStorage.getItem('admin_logs')) || [];
      
      logs.unshift({
        id: logs.length > 0 ? Math.max(...logs.map(l => l.id)) + 1 : 1,
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        operator,
        action,
        details,
        ip: '*************'
      });
      
      localStorage.setItem('admin_logs', JSON.stringify(logs));
      loadLogs();
    }
  </script>
  <script src="js/auth.js"></script>
  <script src="js/admin.js"></script>
</body>
</html>
