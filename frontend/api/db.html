
<!-- xiamen_subway_commercial_management/frontend/api/db.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>厦门地铁商业管理API</title>
  <script src="../js/data.js"></script>
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
      margin: 0;
      padding: 2rem;
      color: #1f2937;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 2rem;
    }
    h1 {
      color: #1e40af;
      border-bottom: 2px solid #3b82f6;
      padding-bottom: 0.5rem;
    }
    .api-endpoint {
      background: #f9fafb;
      border-left: 4px solid #3b82f6;
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0 0.5rem 0.5rem 0;
    }
    .method {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-weight: bold;
      margin-right: 0.5rem;
    }
    .get { background: #dbeafe; color: #1e40af; }
    .post { background: #dcfce7; color: #166534; }
    .put { background: #fef3c7; color: #92400e; }
    .delete { background: #fee2e2; color: #991b1b; }
    .url {
      font-family: monospace;
      background: #f3f4f6;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
    }
    .params {
      margin-top: 0.5rem;
    }
    .param {
      display: inline-block;
      background: #e5e7eb;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      margin-right: 0.5rem;
      font-size: 0.875rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>厦门地铁商业管理API</h1>
    <p>模拟RESTful API接口，通过URL参数区分不同数据操作类型</p>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/spots</span>
      </div>
      <p>获取所有商业点位数据</p>
      <div class="params">
        <span class="param">可选参数: status=leased/vacant</span>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/spots/:id</span>
      </div>
      <p>获取指定ID的商业点位详情</p>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method post">POST</span>
        <span class="url">/api/spots</span>
      </div>
      <p>添加新的商业点位</p>
      <div class="params">
        <span class="param">body: JSON格式点位数据</span>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method put">PUT</span>
        <span class="url">/api/spots/:id</span>
      </div>
      <p>更新指定ID的商业点位信息</p>
      <div class="params">
        <span class="param">body: JSON格式更新数据</span>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method delete">DELETE</span>
        <span class="url">/api/spots/:id</span>
      </div>
      <p>删除指定ID的商业点位</p>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/tenants</span>
      </div>
      <p>获取所有租户数据</p>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/contracts</span>
      </div>
      <p>获取所有合同数据</p>
      <div class="params">
        <span class="param">可选参数: status=active/expiring/expired</span>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/finance/stats</span>
      </div>
      <p>获取财务统计数据</p>
      <div class="params">
        <span class="param">可选参数: year=2025</span>
      </div>
    </div>
  </div>

  <script>
    // 解析URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    const dataType = urlParams.get('type');
    const id = urlParams.get('id');
    
    // 处理API请求
    function handleRequest() {
      let response = { success: false, message: 'Invalid request' };
      
      try {
        switch(action) {
          case 'get':
            if (dataType === 'spots') {
              const status = urlParams.get('status');
              response = {
                success: true,
                data: status ? 
                  DataService.getSpotsByStatus(status) : 
                  DataService.getAllSpots()
              };
            } 
            else if (dataType === 'spots' && id) {
              response = {
                success: true,
                data: DataService.getSpotById(id)
              };
            }
            else if (dataType === 'tenants') {
              response = {
                success: true,
                data: DataService.getAllTenants()
              };
            }
            else if (dataType === 'contracts') {
              const status = urlParams.get('status');
              response = {
                success: true,
                data: status === 'expiring' ? 
                  DataService.getExpiringContracts(30) : 
                  DataService.getAllContracts()
              };
            }
            else if (dataType === 'finance' && urlParams.get('stats')) {
              const year = urlParams.get('year') || new Date().getFullYear();
              response = {
                success: true,
                data: DataService.getFinancialStats(parseInt(year))
              };
            }
            break;
            
          case 'post':
            if (dataType === 'spots') {
              const body = JSON.parse(urlParams.get('body') || '{}');
              response = {
                success: DataService.addSpot(body),
                message: 'Spot added successfully'
              };
            }
            break;
            
          case 'put':
            if (dataType === 'spots' && id) {
              const body = JSON.parse(urlParams.get('body') || '{}');
              response = {
                success: DataService.updateSpot(id, body),
                message: 'Spot updated successfully'
              };
            }
            break;
            
          case 'delete':
            if (dataType === 'spots' && id) {
              // 实际删除逻辑需要在DataService中实现
              response = {
                success: false,
                message: 'Delete operation not implemented'
              };
            }
            break;
        }
      } catch (error) {
        response = {
          success: false,
          message: error.message
        };
      }
      
      // 输出JSON响应
      document.write(JSON.stringify(response, null, 2));
    }
    
    // 如果是API请求则处理
    if (action && dataType) {
      handleRequest();
    }
  </script>
</body>
</html>
