
// xiamen_subway_commercial_management/frontend/api/db.js
/**
 * 模拟后端API服务
 * 使用localStorage实现数据持久化
 */

const ApiService = {
    // 初始化数据存储
    initStorage: function() {
        if (!localStorage.getItem('commercialSpots')) {
            localStorage.setItem('commercialSpots', JSON.stringify([]));
        }
        if (!localStorage.getItem('tenants')) {
            localStorage.setItem('tenants', JSON.stringify([]));
        }
        if (!localStorage.getItem('contracts')) {
            localStorage.setItem('contracts', JSON.stringify([]));
        }
        if (!localStorage.getItem('payments')) {
            localStorage.setItem('payments', JSON.stringify([]));
        }
    },

    // 通用数据获取方法
    getData: function(type, params = {}) {
        const data = JSON.parse(localStorage.getItem(type)) || [];
        
        // 根据参数过滤数据
        if (params.filter) {
            return data.filter(item => {
                return Object.keys(params.filter).every(key => {
                    return item[key] === params.filter[key];
                });
            });
        }
        
        // 根据ID获取单条数据
        if (params.id) {
            return data.find(item => item.id === params.id);
        }
        
        return data;
    },

    // 通用数据添加方法
    addData: function(type, newData) {
        const data = this.getData(type);
        data.push(newData);
        localStorage.setItem(type, JSON.stringify(data));
        return { success: true, data: newData };
    },

    // 通用数据更新方法
    updateData: function(type, id, updatedData) {
        const data = this.getData(type);
        const index = data.findIndex(item => item.id === id);
        
        if (index !== -1) {
            data[index] = { ...data[index], ...updatedData };
            localStorage.setItem(type, JSON.stringify(data));
            return { success: true, data: data[index] };
        }
        
        return { success: false, message: 'Data not found' };
    },

    // 通用数据删除方法
    deleteData: function(type, id) {
        const data = this.getData(type);
        const index = data.findIndex(item => item.id === id);
        
        if (index !== -1) {
            const deletedItem = data.splice(index, 1)[0];
            localStorage.setItem(type, JSON.stringify(data));
            return { success: true, data: deletedItem };
        }
        
        return { success: false, message: 'Data not found' };
    },

    // 获取商业点位数据
    getSpots: function(params = {}) {
        return this.getData('commercialSpots', params);
    },

    // 添加商业点位
    addSpot: function(spotData) {
        return this.addData('commercialSpots', spotData);
    },

    // 更新商业点位
    updateSpot: function(id, updatedData) {
        return this.updateData('commercialSpots', id, updatedData);
    },

    // 删除商业点位
    deleteSpot: function(id) {
        return this.deleteData('commercialSpots', id);
    },

    // 获取租户数据
    getTenants: function(params = {}) {
        return this.getData('tenants', params);
    },

    // 添加租户
    addTenant: function(tenantData) {
        return this.addData('tenants', tenantData);
    },

    // 获取合同数据
    getContracts: function(params = {}) {
        return this.getData('contracts', params);
    },

    // 添加合同
    addContract: function(contractData) {
        // 更新关联点位状态
        if (contractData.spotId) {
            this.updateSpot(contractData.spotId, { status: 'leased' });
        }
        return this.addData('contracts', contractData);
    },

    // 获取支付记录
    getPayments: function(params = {}) {
        return this.getData('payments', params);
    },

    // 添加支付记录
    addPayment: function(paymentData) {
        return this.addData('payments', paymentData);
    },

    // 获取财务统计数据
    getFinancialStats: function(year) {
        const payments = this.getPayments();
        const contracts = this.getContracts();
        const spots = this.getSpots();
        
        const monthlyData = Array(12).fill(0);
        const typeData = {
            '餐饮': 0,
            '零售': 0,
            '服务': 0,
            '广告': 0,
            '其他': 0
        };
        
        payments.forEach(payment => {
            const paymentDate = new Date(payment.date);
            if (paymentDate.getFullYear() === year) {
                const month = paymentDate.getMonth();
                monthlyData[month] += payment.amount;
                
                const contract = contracts.find(c => c.id === payment.contractId);
                if (contract) {
                    const spot = spots.find(s => s.id === contract.spotId);
                    if (spot && spot.type) {
                        typeData[spot.type] = (typeData[spot.type] || 0) + payment.amount;
                    }
                }
            }
        });
        
        return {
            monthlyIncome: monthlyData,
            incomeByType: typeData,
            totalIncome: monthlyData.reduce((sum, amount) => sum + amount, 0)
        };
    },

    // 处理API请求
    handleRequest: function(req) {
        const { method, type, id, data, params } = req;
        
        try {
            switch(method.toLowerCase()) {
                case 'get':
                    return { success: true, data: this.getData(type, { id, filter: params }) };
                case 'post':
                    return this.addData(type, data);
                case 'put':
                    return this.updateData(type, id, data);
                case 'delete':
                    return this.deleteData(type, id);
                default:
                    return { success: false, message: 'Invalid method' };
            }
        } catch (error) {
            return { success: false, message: error.message };
        }
    }
};

// 初始化数据存储
ApiService.initStorage();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiService;
} else {
    window.ApiService = ApiService;
}
