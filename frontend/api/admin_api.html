
<!-- xiamen_subway_commercial_management/frontend/api/admin_api.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>后台管理API文档 - 厦门地铁商业管理系统</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
      margin: 0;
      padding: 2rem;
      color: #1f2937;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 2rem;
    }
    h1 {
      color: #1e40af;
      border-bottom: 2px solid #3b82f6;
      padding-bottom: 0.5rem;
    }
    .api-endpoint {
      background: #f9fafb;
      border-left: 4px solid #3b82f6;
      padding: 1rem;
      margin: 1rem 0;
      border-radius: 0 0.5rem 0.5rem 0;
    }
    .method {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-weight: bold;
      margin-right: 0.5rem;
    }
    .get { background: #dbeafe; color: #1e40af; }
    .post { background: #dcfce7; color: #166534; }
    .put { background: #fef3c7; color: #92400e; }
    .delete { background: #fee2e2; color: #991b1b; }
    .url {
      font-family: monospace;
      background: #f3f4f6;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
    }
    .params {
      margin-top: 0.5rem;
    }
    .param {
      display: inline-block;
      background: #e5e7eb;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      margin-right: 0.5rem;
      font-size: 0.875rem;
    }
    .response {
      margin-top: 1rem;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      padding: 0.75rem;
    }
    .response-title {
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>后台管理API文档</h1>
    <p>后台管理模块的RESTful API接口，通过localStorage实现数据持久化</p>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/admin/users</span>
      </div>
      <p>获取所有用户列表</p>
      <div class="params">
        <span class="param">可选参数: status=active/inactive</span>
        <span class="param">可选参数: role=admin/operator/viewer</span>
      </div>
      <div class="response">
        <div class="response-title">响应示例</div>
        <pre>{
  "success": true,
  "data": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active",
      "avatar": "https://picsum.photos/200/200?random=1"
    }
  ]
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method post">POST</span>
        <span class="url">/api/admin/users</span>
      </div>
      <p>创建新用户</p>
      <div class="params">
        <span class="param">body: JSON格式用户数据</span>
      </div>
      <div class="response">
        <div class="response-title">请求体示例</div>
        <pre>{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>",
  "role": "operator"
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method put">PUT</span>
        <span class="url">/api/admin/users/:id</span>
      </div>
      <p>更新用户信息</p>
      <div class="params">
        <span class="param">body: JSON格式更新数据</span>
      </div>
      <div class="response">
        <div class="response-title">请求体示例</div>
        <pre>{
  "email": "<EMAIL>",
  "role": "admin",
  "status": "active"
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method delete">DELETE</span>
        <span class="url">/api/admin/users/:id</span>
      </div>
      <p>删除用户</p>
      <div class="response">
        <div class="response-title">响应示例</div>
        <pre>{
  "success": true,
  "message": "用户删除成功"
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/admin/settings</span>
      </div>
      <p>获取系统设置</p>
      <div class="response">
        <div class="response-title">响应示例</div>
        <pre>{
  "success": true,
  "data": {
    "system_name": "厦门地铁商业点位管理系统",
    "timezone": "Asia/Shanghai",
    "backup_frequency": "daily",
    "logo_url": "https://picsum.photos/200/200?random=100"
  }
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method put">PUT</span>
        <span class="url">/api/admin/settings</span>
      </div>
      <p>更新系统设置</p>
      <div class="params">
        <span class="param">body: JSON格式设置数据</span>
      </div>
      <div class="response">
        <div class="response-title">请求体示例</div>
        <pre>{
  "system_name": "更新后的系统名称",
  "timezone": "UTC"
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/admin/logs</span>
      </div>
      <p>获取操作日志</p>
      <div class="params">
        <span class="param">可选参数: limit=数量</span>
        <span class="param">可选参数: action=操作类型</span>
      </div>
      <div class="response">
        <div class="response-title">响应示例</div>
        <pre>{
  "success": true,
  "data": [
    {
      "id": 1,
      "timestamp": "2025-07-22T10:30:45",
      "username": "admin",
      "action": "用户登录",
      "ip": "*************"
    }
  ]
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method get">GET</span>
        <span class="url">/api/admin/backup</span>
      </div>
      <p>备份系统数据</p>
      <div class="response">
        <div class="response-title">响应示例</div>
        <pre>{
  "success": true,
  "data": {
    "users": [...],
    "settings": {...},
    "logs": [...],
    "timestamp": "2025-07-22T10:30:45"
  }
}</pre>
      </div>
    </div>
    
    <div class="api-endpoint">
      <div>
        <span class="method post">POST</span>
        <span class="url">/api/admin/restore</span>
      </div>
      <p>恢复系统数据</p>
      <div class="params">
        <span class="param">body: JSON格式备份数据</span>
      </div>
      <div class="response">
        <div class="response-title">响应示例</div>
        <pre>{
  "success": true,
  "message": "数据恢复成功"
}</pre>
      </div>
    </div>
  </div>

  <script>
    // 解析URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    const endpoint = urlParams.get('endpoint');
    const id = urlParams.get('id');
    
    // 处理API请求
    function handleRequest() {
      let response = { success: false, message: 'Invalid request' };
      
      try {
        if (action === 'get' && endpoint === 'admin/users') {
          const status = urlParams.get('status');
          const role = urlParams.get('role');
          const users = JSON.parse(localStorage.getItem('admin_users')) || [];
          
          let filteredUsers = users;
          if (status) filteredUsers = filteredUsers.filter(u => u.status === status);
          if (role) filteredUsers = filteredUsers.filter(u => u.role === role);
          
          response = { success: true, data: filteredUsers };
        }
        else if (action === 'post' && endpoint === 'admin/users') {
          const body = JSON.parse(urlParams.get('body') || '{}');
          const users = JSON.parse(localStorage.getItem('admin_users')) || [];
          
          const newUser = {
            id: users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1,
            ...body,
            status: 'active',
            avatar: `https://picsum.photos/200/200?random=${Math.floor(Math.random() * 1000)}`
          };
          
          users.push(newUser);
          localStorage.setItem('admin_users', JSON.stringify(users));
          response = { success: true, data: newUser };
        }
        else if (action === 'put' && endpoint === 'admin/users' && id) {
          const body = JSON.parse(urlParams.get('body') || '{}');
          const users = JSON.parse(localStorage.getItem('admin_users')) || [];
          const index = users.findIndex(u => u.id === parseInt(id));
          
          if (index !== -1) {
            users[index] = { ...users[index], ...body };
            localStorage.setItem('admin_users', JSON.stringify(users));
            response = { success: true, data: users[index] };
          }
        }
        else if (action === 'delete' && endpoint === 'admin/users' && id) {
          const users = JSON.parse(localStorage.getItem('admin_users')) || [];
          const index = users.findIndex(u => u.id === parseInt(id));
          
          if (index !== -1) {
            const deletedUser = users.splice(index, 1)[0];
            localStorage.setItem('admin_users', JSON.stringify(users));
            response = { success: true, message: '用户删除成功' };
          }
        }
        else if (action === 'get' && endpoint === 'admin/settings') {
          const settings = JSON.parse(localStorage.getItem('admin_settings')) || {};
          response = { success: true, data: settings };
        }
        else if (action === 'put' && endpoint === 'admin/settings') {
          const body = JSON.parse(urlParams.get('body') || '{}');
          const settings = JSON.parse(localStorage.getItem('admin_settings')) || {};
          
          const newSettings = { ...settings, ...body };
          localStorage.setItem('admin_settings', JSON.stringify(newSettings));
          response = { success: true, data: newSettings };
        }
        else if (action === 'get' && endpoint === 'admin/logs') {
          const limit = parseInt(urlParams.get('limit')) || 50;
          const actionFilter = urlParams.get('action');
          let logs = JSON.parse(localStorage.getItem('admin_logs')) || [];
          
          if (actionFilter) logs = logs.filter(l => l.action === actionFilter);
          logs = logs.slice(0, limit);
          
          response = { success: true, data: logs };
        }
        else if (action === 'get' && endpoint === 'admin/backup') {
          const backup = {
            users: JSON.parse(localStorage.getItem('admin_users')) || [],
            settings: JSON.parse(localStorage.getItem('admin_settings')) || {},
            logs: JSON.parse(localStorage.getItem('admin_logs')) || [],
            timestamp: new Date().toISOString()
          };
          response = { success: true, data: backup };
        }
        else if (action === 'post' && endpoint === 'admin/restore') {
          const body = JSON.parse(urlParams.get('body') || '{}');
          
          if (body.users) localStorage.setItem('admin_users', JSON.stringify(body.users));
          if (body.settings) localStorage.setItem('admin_settings', JSON.stringify(body.settings));
          if (body.logs) localStorage.setItem('admin_logs', JSON.stringify(body.logs));
          
          response = { success: true, message: '数据恢复成功' };
        }
      } catch (error) {
        response = {
          success: false,
          message: error.message
        };
      }
      
      // 输出JSON响应
      document.write(JSON.stringify(response, null, 2));
    }
    
    // 如果是API请求则处理
    if (action && endpoint) {
      handleRequest();
    }
  </script>
</body>
</html>
