
<!-- x<PERSON>en_subway_commercial_management/frontend/map.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>点位地图 - 厦门地铁商业点位管理系统</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }
    .nav-item {
      transition: all 0.3s ease;
    }
    .nav-item:hover {
      transform: translateY(-2px);
    }
    .card {
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    #particles-js {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    #map-container {
      height: 600px;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body class="antialiased">
  <div id="particles-js"></div>
  
  <header class="bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg">
    <div class="container mx-auto px-4 py-6">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
          </svg>
          <h1 class="text-2xl font-bold">厦门地铁商业点位管理系统</h1>
        </div>
        <nav class="hidden md:flex space-x-8">
          <a href="index.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">首页</a>
          <a href="map.html" class="nav-item font-medium px-3 py-2 rounded-md bg-blue-700">点位地图</a>
          <a href="leasing.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">租赁管理</a>
          <a href="finance.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">财务统计</a>
        </nav>
        <div class="flex items-center space-x-4">
          <div class="relative">
            <button class="p-2 rounded-full hover:bg-blue-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </button>
            <span class="absolute top-0 right-0 h-3 w-3 rounded-full bg-red-500"></span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="https://picsum.photos/200/200?random=1" alt="用户头像" class="h-10 w-10 rounded-full">
            <span class="font-medium">管理员</span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <div class="mb-8">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold text-gray-800">商业点位地图</h2>
        <div class="flex space-x-4">
          <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            添加点位
          </button>
          <div class="relative">
            <select class="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option>全部点位</option>
              <option>已出租</option>
              <option>空置中</option>
              <option>即将到期</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div class="lg:col-span-3">
          <div id="map-container" class="card bg-white">
            <iframe src="https://map.qq.com/" frameborder="0" style="width:100%;height:100%;"></iframe>
          </div>
        </div>
        
        <div>
          <div class="card bg-white p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">点位状态统计</h3>
            <div class="space-y-4">
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-sm font-medium text-gray-700">已出租</span>
                  <span class="text-sm font-medium text-gray-700">76%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" style="width: 76%"></div>
                </div>
              </div>
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-sm font-medium text-gray-700">空置中</span>
                  <span class="text-sm font-medium text-gray-700">24%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-yellow-500 h-2 rounded-full" style="width: 24%"></div>
                </div>
              </div>
            </div>
            
            <div class="mt-8">
              <h4 class="text-lg font-medium text-gray-800 mb-3">最近更新</h4>
              <div class="space-y-3">
                <div class="flex items-start">
                  <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 rounded-full bg-blue-500"></div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-700">XM-2025-112 点位已签约</p>
                    <p class="text-xs text-gray-500">2025-07-20 14:30</p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 rounded-full bg-green-500"></div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-700">XM-2025-078 审核通过</p>
                    <p class="text-xs text-gray-500">2025-07-18 10:15</p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0 mt-1">
                    <div class="h-2 w-2 rounded-full bg-red-500"></div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-700">XM-2025-045 合同即将到期</p>
                    <p class="text-xs text-gray-500">2025-07-15 16:45</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card bg-white p-6">
      <h3 class="text-xl font-bold text-gray-800 mb-4">点位列表</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位编号</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">位置</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">面积(m²)</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XM-2025-001</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">厦门地铁1号线 镇海路站</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已出租</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                <button class="text-blue-600 hover:text-blue-900">编辑</button>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XM-2025-002</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">厦门地铁1号线 中山公园站</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">32</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">空置中</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                <button class="text-blue-600 hover:text-blue-900">编辑</button>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XM-2025-003</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">厦门地铁2号线 五缘湾站</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">60</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已出租</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                <button class="text-blue-600 hover:text-blue-900">编辑</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </main>

  <footer class="bg-gray-800 text-white py-8 mt-12">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-lg font-bold mb-4">厦门地铁商业点位管理系统</h3>
          <p class="text-gray-400">专业的地铁商业资产管理平台，助力商业运营效率提升。</p>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li><a href="map.html" class="text-gray-400 hover:text-white">点位地图</a></li>
            <li><a href="leasing.html" class="text-gray-400 hover:text-white">租赁管理</a></li>
            <li><a href="finance.html" class="text-gray-400 hover:text-white">财务统计</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">联系我们</h3>
          <p class="text-gray-400">电话：0592-12345678</p>
          <p class="text-gray-400">邮箱：<EMAIL></p>
        </div>
      </div>
      <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
        <p>© 2025 厦门地铁商业管理有限公司. 保留所有权利.</p>
      </div>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
  <script>
    // 粒子效果初始化
    document.addEventListener('DOMContentLoaded', function() {
      particlesJS('particles-js', {
        particles: {
          number: { value: 80, density: { enable: true, value_area: 800 } },
          color: { value: "#3b82f6" },
          shape: { type: "circle" },
          opacity: { value: 0.5, random: true },
          size: { value: 3, random: true },
          line_linked: { enable: true, distance: 150, color: "#3b82f6", opacity: 0.4, width: 1 },
          move: { enable: true, speed: 2, direction: "none", random: true, straight: false, out_mode: "out" }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: { enable: true, mode: "grab" },
            onclick: { enable: true, mode: "push" }
          }
        }
      });
    });
  </script>
  <script src="js/app.js"></script>
</body>
</html>
