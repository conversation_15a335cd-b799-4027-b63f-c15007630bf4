
<!-- xiamen_subway_commercial_management/frontend/js/admin.js -->
/**
 * 后台管理模块
 * 处理用户管理、系统设置等后台操作
 * 与DataService和AuthService集成
 */

const AdminService = {
    // 初始化管理数据
    init: function() {
        if (!localStorage.getItem('admin_users')) {
            const defaultUsers = [
                {
                    id: 1,
                    username: 'admin',
                    password: 'admin123',
                    email: '<EMAIL>',
                    role: 'admin',
                    status: 'active',
                    avatar: 'https://picsum.photos/200/200?random=1',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    username: 'operator',
                    password: 'operator123',
                    email: '<EMAIL>',
                    role: 'operator',
                    status: 'active',
                    avatar: 'https://picsum.photos/200/200?random=2',
                    createdAt: new Date().toISOString()
                }
            ];
            localStorage.setItem('admin_users', JSON.stringify(defaultUsers));
        }

        if (!localStorage.getItem('admin_settings')) {
            const defaultSettings = {
                system_name: '厦门地铁商业点位管理系统',
                timezone: 'Asia/Shanghai',
                backup_frequency: 'daily',
                logo_url: 'https://picsum.photos/200/200?random=100',
                theme: 'light'
            };
            localStorage.setItem('admin_settings', JSON.stringify(defaultSettings));
        }

        if (!localStorage.getItem('admin_logs')) {
            localStorage.setItem('admin_logs', JSON.stringify([]));
        }
    },

    // 获取所有用户
    getUsers: function() {
        return JSON.parse(localStorage.getItem('admin_users')) || [];
    },

    // 添加新用户
    addUser: function(userData) {
        const users = this.getUsers();
        const newUser = {
            id: users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1,
            ...userData,
            status: 'active',
            avatar: `https://picsum.photos/200/200?random=${Math.floor(Math.random() * 1000)}`,
            createdAt: new Date().toISOString()
        };

        users.push(newUser);
        localStorage.setItem('admin_users', JSON.stringify(users));
        
        // 记录操作日志
        this.addLog('添加用户', `添加了新用户: ${newUser.username}`);
        
        return newUser;
    },

    // 更新用户信息
    updateUser: function(userId, updatedData) {
        const users = this.getUsers();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex !== -1) {
            users[userIndex] = { ...users[userIndex], ...updatedData };
            localStorage.setItem('admin_users', JSON.stringify(users));
            
            // 记录操作日志
            this.addLog('更新用户', `更新了用户ID: ${userId}的信息`);
            
            return users[userIndex];
        }
        
        return null;
    },

    // 删除用户
    deleteUser: function(userId) {
        const users = this.getUsers();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex !== -1) {
            const deletedUser = users.splice(userIndex, 1)[0];
            localStorage.setItem('admin_users', JSON.stringify(users));
            
            // 记录操作日志
            this.addLog('删除用户', `删除了用户: ${deletedUser.username}`);
            
            return deletedUser;
        }
        
        return null;
    },

    // 切换用户状态
    toggleUserStatus: function(userId) {
        const users = this.getUsers();
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex !== -1) {
            const newStatus = users[userIndex].status === 'active' ? 'inactive' : 'active';
            users[userIndex].status = newStatus;
            localStorage.setItem('admin_users', JSON.stringify(users));
            
            // 记录操作日志
            this.addLog('修改用户状态', `将用户ID: ${userId}状态改为: ${newStatus}`);
            
            return users[userIndex];
        }
        
        return null;
    },

    // 获取系统设置
    getSettings: function() {
        return JSON.parse(localStorage.getItem('admin_settings')) || {};
    },

    // 更新系统设置
    updateSettings: function(newSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        localStorage.setItem('admin_settings', JSON.stringify(updatedSettings));
        
        // 记录操作日志
        this.addLog('更新系统设置', '修改了系统配置参数');
        
        return updatedSettings;
    },

    // 获取操作日志
    getLogs: function(limit = 50) {
        const logs = JSON.parse(localStorage.getItem('admin_logs')) || [];
        return logs.slice(0, limit);
    },

    // 添加操作日志
    addLog: function(action, details = '') {
        const currentUser = AuthService.getCurrentUser();
        const logs = this.getLogs(1000); // 获取所有日志
        
        const newLog = {
            id: logs.length > 0 ? Math.max(...logs.map(l => l.id)) + 1 : 1,
            timestamp: new Date().toISOString(),
            username: currentUser ? currentUser.username : 'system',
            action,
            details,
            ip: '***********' // 模拟IP地址
        };
        
        logs.unshift(newLog);
        localStorage.setItem('admin_logs', JSON.stringify(logs));
    },

    // 备份系统数据
    backupData: function() {
        const backup = {
            users: this.getUsers(),
            settings: this.getSettings(),
            logs: this.getLogs(1000),
            timestamp: new Date().toISOString(),
            version: '1.0'
        };
        
        // 记录操作日志
        this.addLog('数据备份', '执行了系统数据备份');
        
        return backup;
    },

    // 恢复系统数据
    restoreData: function(backup) {
        if (backup.users) {
            localStorage.setItem('admin_users', JSON.stringify(backup.users));
        }
        
        if (backup.settings) {
            localStorage.setItem('admin_settings', JSON.stringify(backup.settings));
        }
        
        if (backup.logs) {
            localStorage.setItem('admin_logs', JSON.stringify(backup.logs));
        }
        
        // 记录操作日志
        this.addLog('数据恢复', '从备份恢复了系统数据');
        
        return true;
    },

    // 重置系统
    resetSystem: function() {
        localStorage.removeItem('admin_users');
        localStorage.removeItem('admin_settings');
        localStorage.removeItem('admin_logs');
        
        // 重新初始化
        this.init();
        
        // 记录操作日志
        this.addLog('系统重置', '将系统重置为初始状态');
        
        return true;
    }
};

// 初始化AdminService
AdminService.init();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminService;
} else {
    window.AdminService = AdminService;
}
