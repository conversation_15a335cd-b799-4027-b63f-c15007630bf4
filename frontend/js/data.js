
// xiamen_subway_commercial_management/frontend/js/data.js
const DataService = {
  // 初始化本地存储
  initStorage: function() {
    if (!localStorage.getItem('commercialSpots')) {
      const spots = [
        {
          id: 'XM-2025-001',
          name: '镇海路站1号点位',
          line: '1号线',
          station: '镇海路站',
          area: 45,
          type: '餐饮',
          status: 'leased',
          tenant: '星巴克咖啡',
          rent: 45000,
          contractStart: '2023-01-01',
          contractEnd: '2025-12-31',
          paymentStatus: 'paid',
          images: [
            'https://picsum.photos/400/300?random=101',
            'https://picsum.photos/400/300?random=102'
          ]
        },
        {
          id: 'XM-2025-002',
          name: '中山公园站2号点位',
          line: '1号线',
          station: '中山公园站',
          area: 32,
          type: '零售',
          status: 'vacant',
          tenant: null,
          rent: 32000,
          contractStart: null,
          contractEnd: null,
          paymentStatus: null,
          images: [
            'https://picsum.photos/400/300?random=103',
            'https://picsum.photos/400/300?random=104'
          ]
        }
      ];
      localStorage.setItem('commercialSpots', JSON.stringify(spots));
    }

    if (!localStorage.getItem('tenants')) {
      const tenants = [
        {
          id: 'T-2023-001',
          name: '星巴克咖啡',
          contact: '张经理',
          phone: '***********',
          email: '<EMAIL>',
          businessType: '餐饮',
          contractIds: ['XM-HT-2023-001'],
          rating: 4.5,
          notes: '长期合作客户'
        }
      ];
      localStorage.setItem('tenants', JSON.stringify(tenants));
    }

    if (!localStorage.getItem('contracts')) {
      const contracts = [
        {
          id: 'XM-HT-2023-001',
          spotId: 'XM-2025-001',
          tenantId: 'T-2023-001',
          startDate: '2023-01-01',
          endDate: '2025-12-31',
          monthlyRent: 45000,
          deposit: 90000,
          paymentDay: 15,
          terms: '3年合同，每年租金递增5%',
          status: 'active',
          attachments: []
        }
      ];
      localStorage.setItem('contracts', JSON.stringify(contracts));
    }

    if (!localStorage.getItem('payments')) {
      const payments = [
        {
          id: 'PY-202307-001',
          contractId: 'XM-HT-2023-001',
          amount: 45000,
          date: '2023-07-15',
          status: 'paid',
          method: 'bank',
          invoiceNo: 'INV202307001'
        }
      ];
      localStorage.setItem('payments', JSON.stringify(payments));
    }
  },

  // 获取所有商业点位
  getAllSpots: function() {
    const spots = JSON.parse(localStorage.getItem('commercialSpots')) || [];
    return spots;
  },

  // 获取特定状态的点位
  getSpotsByStatus: function(status) {
    const spots = this.getAllSpots();
    return spots.filter(spot => spot.status === status);
  },

  // 获取单个点位详情
  getSpotById: function(id) {
    const spots = this.getAllSpots();
    return spots.find(spot => spot.id === id);
  },

  // 添加新点位
  addSpot: function(spotData) {
    const spots = this.getAllSpots();
    spots.push(spotData);
    localStorage.setItem('commercialSpots', JSON.stringify(spots));
    return true;
  },

  // 更新点位信息
  updateSpot: function(id, updatedData) {
    const spots = this.getAllSpots();
    const index = spots.findIndex(spot => spot.id === id);
    if (index !== -1) {
      spots[index] = {...spots[index], ...updatedData};
      localStorage.setItem('commercialSpots', JSON.stringify(spots));
      return true;
    }
    return false;
  },

  // 获取所有租户
  getAllTenants: function() {
    return JSON.parse(localStorage.getItem('tenants')) || [];
  },

  // 获取单个租户详情
  getTenantById: function(id) {
    const tenants = this.getAllTenants();
    return tenants.find(tenant => tenant.id === id);
  },

  // 添加新租户
  addTenant: function(tenantData) {
    const tenants = this.getAllTenants();
    tenants.push(tenantData);
    localStorage.setItem('tenants', JSON.stringify(tenants));
    return true;
  },

  // 获取所有合同
  getAllContracts: function() {
    return JSON.parse(localStorage.getItem('contracts')) || [];
  },

  // 获取即将到期的合同
  getExpiringContracts: function(days = 30) {
    const contracts = this.getAllContracts();
    const today = new Date();
    return contracts.filter(contract => {
      const endDate = new Date(contract.endDate);
      const diffTime = endDate - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= days && diffDays >= 0;
    });
  },

  // 添加新合同
  addContract: function(contractData) {
    const contracts = this.getAllContracts();
    contracts.push(contractData);
    localStorage.setItem('contracts', JSON.stringify(contracts));
    
    // 更新点位状态
    if (contractData.spotId) {
      this.updateSpot(contractData.spotId, {status: 'leased'});
    }
    return true;
  },

  // 获取所有支付记录
  getAllPayments: function() {
    return JSON.parse(localStorage.getItem('payments')) || [];
  },

  // 添加支付记录
  addPayment: function(paymentData) {
    const payments = this.getAllPayments();
    payments.push(paymentData);
    localStorage.setItem('payments', JSON.stringify(payments));
    return true;
  },

  // 获取财务统计数据
  getFinancialStats: function(year) {
    const payments = this.getAllPayments();
    const contracts = this.getAllContracts();
    
    const monthlyData = Array(12).fill(0);
    const typeData = {
      '餐饮': 0,
      '零售': 0,
      '服务': 0,
      '广告': 0,
      '其他': 0
    };
    
    payments.forEach(payment => {
      const paymentDate = new Date(payment.date);
      if (paymentDate.getFullYear() === year) {
        const month = paymentDate.getMonth();
        monthlyData[month] += payment.amount;
        
        const contract = contracts.find(c => c.id === payment.contractId);
        if (contract) {
          const spot = this.getSpotById(contract.spotId);
          if (spot && spot.type) {
            typeData[spot.type] = (typeData[spot.type] || 0) + payment.amount;
          }
        }
      }
    });
    
    return {
      monthlyIncome: monthlyData,
      incomeByType: typeData,
      totalIncome: monthlyData.reduce((sum, amount) => sum + amount, 0)
    };
  },

  // 获取点位地图数据
  getMapData: function() {
    const spots = this.getAllSpots();
    return spots.map(spot => ({
      id: spot.id,
      name: spot.name,
      line: spot.line,
      station: spot.station,
      status: spot.status,
      coordinates: this.getStationCoordinates(spot.station),
      popupContent: `
        <h3>${spot.name}</h3>
        <p>${spot.line} ${spot.station}站</p>
        <p>面积: ${spot.area}m²</p>
        <p>状态: ${spot.status === 'leased' ? '已出租' : '空置中'}</p>
        ${spot.status === 'leased' ? `<p>租户: ${spot.tenant}</p>` : ''}
      `
    }));
  },

  // 模拟获取地铁站坐标
  getStationCoordinates: function(station) {
    // 实际应用中应使用真实坐标数据
    const stationCoords = {
      '镇海路站': [24.4567, 118.0789],
      '中山公园站': [24.4589, 118.0821],
      '五缘湾站': [24.5321, 118.1567]
    };
    return stationCoords[station] || [24.4567, 118.0789];
  },

  // 获取系统通知
  getNotifications: function() {
    return [
      {
        id: 1,
        title: '系统维护通知',
        content: '系统将于2025-08-01 02:00-06:00进行维护升级',
        date: '2025-07-20',
        type: 'system'
      },
      {
        id: 2,
        title: '合同即将到期',
        content: 'XM-HT-2023-045 麦当劳合同将于7天后到期',
        date: '2025-07-15',
        type: 'contract'
      }
    ];
  }
};

// 初始化数据
DataService.initStorage();

// 导出数据服务
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DataService;
} else {
  window.DataService = DataService;
}
