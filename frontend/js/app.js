
// xiamen_subway_commercial_management/frontend/js/app.js
document.addEventListener('DOMContentLoaded', function() {
    // 用户菜单交互功能
    const userMenuButtons = document.querySelectorAll('.user-menu');
    
    userMenuButtons.forEach(menu => {
        const button = menu.querySelector('#user-menu-button');
        const dropdown = menu.querySelector('.dropdown-menu');
        
        if (button && dropdown) {
            // 点击按钮切换下拉菜单显示状态
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                dropdown.classList.toggle('show');
            });
            
            // 点击页面其他区域关闭下拉菜单
            document.addEventListener('click', function() {
                dropdown.classList.remove('show');
            });
            
            // 阻止下拉菜单内部的点击事件冒泡
            dropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }
    });

    // 页面路由检测
    const path = window.location.pathname.split('/').pop();
    
    // 通用功能初始化
    initCommonComponents();
    
    // 页面特定初始化
    switch(path) {
        case 'index.html':
            initDashboard();
            break;
        case 'map.html':
            initMapPage();
            break;
        case 'leasing.html':
            initLeasingPage();
            break;
        case 'finance.html':
            initFinancePage();
            break;
        case 'admin.html':
            initAdminPage();
            break;
    }

    // 初始化通知功能
    initNotifications();
});

// 初始化通用组件
function initCommonComponents() {
    // 所有卡片悬停效果
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 15px rgba(0, 0, 0, 0.1)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}

// 初始化管理员页面
function initAdminPage() {
    // 加载用户数据
    loadUsers();
    // 加载日志数据
    loadLogs();
    // 加载系统设置
    loadSystemSettings();
}

// 其他原有函数保持不变...
