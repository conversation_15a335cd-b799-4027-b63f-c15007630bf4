
// xiamen_subway_commercial_management/frontend/js/auth.js
/**
 * 权限控制模块
 * 处理用户登录状态和权限验证
 * 使用sessionStorage存储登录状态
 */

const AuthService = {
    // 初始化默认用户数据
    initUsers: function() {
        if (!localStorage.getItem('admin_users')) {
            const defaultUsers = [
                {
                    username: 'admin',
                    password: 'admin123',
                    email: '<EMAIL>',
                    role: 'admin',
                    avatar: 'https://picsum.photos/200/200?random=1'
                },
                {
                    username: 'operator',
                    password: 'operator123',
                    email: '<EMAIL>',
                    role: 'operator',
                    avatar: 'https://picsum.photos/200/200?random=2'
                }
            ];
            localStorage.setItem('admin_users', JSON.stringify(defaultUsers));
        }
    },

    // 用户登录
    login: function(username, password, rememberMe) {
        const users = JSON.parse(localStorage.getItem('admin_users')) || [];
        const user = users.find(u => u.username === username && u.password === password);
        
        if (user) {
            const sessionData = {
                isLoggedIn: true,
                username: user.username,
                role: user.role,
                loginTime: new Date().getTime()
            };
            
            // 存储登录状态
            sessionStorage.setItem('auth', JSON.stringify(sessionData));
            
            // 如果选择记住我，则存储在localStorage
            if (rememberMe) {
                localStorage.setItem('rememberedUser', JSON.stringify({
                    username: user.username,
                    token: btoa(`${username}:${password}`)
                }));
            }
            
            return {
                success: true,
                user: {
                    username: user.username,
                    role: user.role,
                    avatar: user.avatar
                }
            };
        }
        
        return {
            success: false,
            message: '用户名或密码错误'
        };
    },

    // 检查登录状态
    checkLogin: function() {
        const authData = sessionStorage.getItem('auth');
        if (authData) {
            return JSON.parse(authData);
        }
        
        // 检查记住的用户
        const rememberedUser = localStorage.getItem('rememberedUser');
        if (rememberedUser) {
            try {
                const userData = JSON.parse(rememberedUser);
                const [username, password] = atob(userData.token).split(':');
                return this.login(username, password, true);
            } catch (e) {
                this.logout();
                return { success: false };
            }
        }
        
        return { success: false };
    },

    // 检查用户权限
    checkPermission: function(requiredRole) {
        const authData = this.checkLogin();
        if (!authData.success) return false;
        
        // 管理员拥有所有权限
        if (authData.user.role === 'admin') return true;
        
        // 检查角色权限
        switch(requiredRole) {
            case 'admin':
                return authData.user.role === 'admin';
            case 'operator':
                return ['admin', 'operator'].includes(authData.user.role);
            case 'viewer':
                return true;
            default:
                return false;
        }
    },

    // 获取当前用户信息
    getCurrentUser: function() {
        const authData = this.checkLogin();
        if (authData.success) {
            return authData.user;
        }
        return null;
    },

    // 用户登出
    logout: function() {
        sessionStorage.removeItem('auth');
        localStorage.removeItem('rememberedUser');
        return { success: true };
    },

    // 路由守卫
    routeGuard: function(requiredRole) {
        return function(to, from, next) {
            const authData = AuthService.checkLogin();
            
            if (!authData.success) {
                next('/login.html');
                return;
            }
            
            if (!AuthService.checkPermission(requiredRole)) {
                next('/403.html');
                return;
            }
            
            next();
        };
    }
};

// 初始化用户数据
AuthService.initUsers();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthService;
} else {
    window.AuthService = AuthService;
}
