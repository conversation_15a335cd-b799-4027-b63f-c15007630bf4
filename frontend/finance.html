
<!-- x<PERSON>en_subway_commercial_management/frontend/finance.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>财务统计 - 厦门地铁商业点位管理系统</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }
    .nav-item {
      transition: all 0.3s ease;
    }
    .nav-item:hover {
      transform: translateY(-2px);
    }
    .card {
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    #particles-js {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    .chart-container {
      position: relative;
      height: 300px;
    }
  </style>
</head>
<body class="antialiased">
  <div id="particles-js"></div>
  
  <header class="bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg">
    <div class="container mx-auto px-4 py-6">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
          </svg>
          <h1 class="text-2xl font-bold">厦门地铁商业点位管理系统</h1>
        </div>
        <nav class="hidden md:flex space-x-8">
          <a href="index.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">首页</a>
          <a href="map.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">点位地图</a>
          <a href="leasing.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">租赁管理</a>
          <a href="finance.html" class="nav-item font-medium px-3 py-2 rounded-md bg-blue-700">财务统计</a>
        </nav>
        <div class="flex items-center space-x-4">
          <div class="relative">
            <button class="p-2 rounded-full hover:bg-blue-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </button>
            <span class="absolute top-0 right-0 h-3 w-3 rounded-full bg-red-500"></span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="https://picsum.photos/200/200?random=1" alt="用户头像" class="h-10 w-10 rounded-full">
            <span class="font-medium">管理员</span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <h2 class="text-3xl font-bold text-gray-800">财务统计</h2>
      <div class="flex space-x-4">
        <div class="relative">
          <select class="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option>2025年</option>
            <option>2024年</option>
            <option>2023年</option>
          </select>
          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          导出报表
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
      <div class="card bg-white rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-xl font-bold text-gray-800">年度总收入</h3>
          <span class="text-sm text-gray-500">2025年</span>
        </div>
        <h2 class="text-4xl font-bold text-blue-600 mb-2">¥15,360,000</h2>
        <p class="text-green-500 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
          12.5% 同比增长
        </p>
      </div>
      
      <div class="card bg-white rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-xl font-bold text-gray-800">年度总支出</h3>
          <span class="text-sm text-gray-500">2025年</span>
        </div>
        <h2 class="text-4xl font-bold text-purple-600 mb-2">¥3,840,000</h2>
        <p class="text-green-500 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
          8.2% 同比增长
        </p>
      </div>
      
      <div class="card bg-white rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-xl font-bold text-gray-800">年度净利润</h3>
          <span class="text-sm text-gray-500">2025年</span>
        </div>
        <h2 class="text-4xl font-bold text-green-600 mb-2">¥11,520,000</h2>
        <p class="text-green-500 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
          14.3% 同比增长
        </p>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <div class="card bg-white rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-bold text-gray-800">月度收入趋势</h3>
          <div class="relative">
            <select class="appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-6 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option>按收入</option>
              <option>按租户</option>
              <option>按点位</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-1 text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="incomeChart"></canvas>
        </div>
      </div>
      
      <div class="card bg-white rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-bold text-gray-800">收入构成</h3>
          <div class="relative">
            <select class="appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-6 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option>按类型</option>
              <option>按线路</option>
              <option>按区域</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-1 text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="revenueChart"></canvas>
        </div>
      </div>
    </div>

    <div class="card bg-white rounded-lg p-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-bold text-gray-800">租金收缴情况</h3>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition">导出Excel</button>
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition">打印</button>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">租户名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位编号</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">应收金额</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">实收金额</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">收缴率</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">星巴克咖啡</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">XM-2025-001</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥45,000</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥45,000</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已缴清</span>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">肯德基</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">XM-2025-002</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥38,000</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥30,400</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-yellow-500 h-2 rounded-full" style="width: 80%"></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">部分缴纳</span>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">周黑鸭</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">XM-2025-003</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥32,000</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥0</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div class="bg-red-500 h-2 rounded-full" style="width: 0%"></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">未缴纳</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </main>

  <footer class="bg-gray-800 text-white py-8 mt-12">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-lg font-bold mb-4">厦门地铁商业点位管理系统</h3>
          <p class="text-gray-400">专业的地铁商业资产管理平台，助力商业运营效率提升。</p>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li><a href="map.html" class="text-gray-400 hover:text-white">点位地图</a></li>
            <li><a href="leasing.html" class="text-gray-400 hover:text-white">租赁管理</a></li>
            <li><a href="finance.html" class="text-gray-400 hover:text-white">财务统计</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">联系我们</h3>
          <p class="text-gray-400">电话：0592-12345678</p>
          <p class="text-gray-400">邮箱：<EMAIL></p>
        </div>
      </div>
      <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
        <p>© 2025 厦门地铁商业管理有限公司. 保留所有权利.</p>
      </div>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // 粒子效果初始化
    document.addEventListener('DOMContentLoaded', function() {
      particlesJS('particles-js', {
        particles: {
          number: { value: 80, density: { enable: true, value_area: 800 } },
          color: { value: "#3b82f6" },
          shape: { type: "circle" },
          opacity: { value: 0.5, random: true },
          size: { value: 3, random: true },
          line_linked: { enable: true, distance: 150, color: "#3b82f6", opacity: 0.4, width: 1 },
          move: { enable: true, speed: 2, direction: "none", random: true, straight: false, out_mode: "out" }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: { enable: true, mode: "grab" },
            onclick: { enable: true, mode: "push" }
          }
        }
      });

      // 收入趋势图表
      const incomeCtx = document.getElementById('incomeChart').getContext('2d');
      const incomeChart = new Chart(incomeCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          datasets: [{
            label: '月收入 (万元)',
            data: [120, 118, 125, 130, 128, 135, 140, 138, 145, 150, 148, 155],
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderColor: 'rgba(59, 130, 246, 1)',
            borderWidth: 2,
            tension: 0.3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });

      // 收入构成图表
      const revenueCtx = document.getElementById('revenueChart').getContext('2d');
      const revenueChart = new Chart(revenueCtx, {
        type: 'doughnut',
        data: {
          labels: ['餐饮', '零售', '服务', '广告', '其他'],
          datasets: [{
            data: [45, 25, 15, 10, 5],
            backgroundColor: [
              'rgba(59, 130, 246, 0.7)',
              'rgba(16, 185, 129, 0.7)',
              'rgba(245, 158, 11, 0.7)',
              'rgba(139, 92, 246, 0.7)',
              'rgba(156, 163, 175, 0.7)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
            }
          }
        }
      });
    });
  </script>
  <script src="js/app.js"></script>
</body>
</html>
