
/* xiamen_subway_commercial_management/frontend/css/style.css */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #1e40af;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --light-gray: #f3f4f6;
  --dark-gray: #6b7280;
}

body {
  font-family: 'Noto Sans SC', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  margin: 0;
  padding: 0;
  color: #1f2937;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header {
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  color: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.nav-item {
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
}

.nav-item:hover {
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 0.1);
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.status-badge {
  display: inline-flex;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.status-danger {
  background-color: #fee2e2;
  color: #991b1b;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: #f9fafb;
  text-align: left;
  padding: 0.75rem 1.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* 用户菜单样式 */
.user-menu {
  position: relative;
  display: inline-block;
}

.user-menu-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.user-menu-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 0.5rem;
  min-width: 200px;
  background-color: white;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 50;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #4a5568;
  transition: all 0.2s;
  text-decoration: none;
}

.dropdown-item:hover {
  background-color: #f7fafc;
  color: #4299e1;
}

.dropdown-item svg {
  margin-right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
}

.dropdown-divider {
  height: 1px;
  margin: 0.5rem 0;
  background-color: #e2e8f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header .nav {
    display: none;
  }

  .grid-cols-1 {
    grid-template-columns: 1fr;
  }

  .table-responsive {
    overflow-x: auto;
  }

  .table th, .table td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .dropdown-menu {
    right: auto;
    left: 0;
    min-width: 180px;
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* 粒子效果容器 */
#particles-js {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: -1;
  top: 0;
  left: 0;
}
