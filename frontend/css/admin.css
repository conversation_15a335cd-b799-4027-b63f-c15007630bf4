
/* xiamen_subway_commercial_management/frontend/css/admin.css */
/* 后台管理专用样式表 - 扩展主样式体系 */

/* 侧边栏样式 */
.sidebar {
    background: linear-gradient(to bottom, #ffffff 0%, #f8fafc 100%);
    border-right: 1px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-item {
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    margin: 0.25rem 0;
    color: #4a5568;
    font-weight: 500;
    transition: all 0.2s ease;
}

.sidebar-item:hover {
    background-color: rgba(59, 130, 246, 0.1);
    color: #1e40af;
}

.sidebar-item.active {
    background-color: #ebf4ff;
    color: #3b82f6;
}

/* 表格样式增强 */
.admin-table {
    border-collapse: separate;
    border-spacing: 0;
}

.admin-table th {
    background-color: #f7fafc;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid #e2e8f0;
}

.admin-table tr:hover td {
    background-color: #f8fafc;
}

/* 模态框动画 */
.modal-enter {
    opacity: 0;
    transform: translateY(-10px);
}

.modal-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease-out;
}

.modal-exit {
    opacity: 1;
}

.modal-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
}

.status-inactive {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

/* 表单控件增强 */
.admin-input {
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.admin-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    border-color: #3b82f6;
}

/* 按钮变体 */
.btn-admin {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-admin-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-admin-primary:hover {
    background-color: #2563eb;
}

.btn-admin-danger {
    background-color: #ef4444;
    color: white;
}

.btn-admin-danger:hover {
    background-color: #dc2626;
}

/* 卡片增强 */
.admin-card {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.admin-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .admin-table {
        display: block;
        overflow-x: auto;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
}

/* 数据可视化图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 操作日志时间线 */
.timeline {
    position: relative;
    padding-left: 1.5rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e2e8f0;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: #3b82f6;
}
