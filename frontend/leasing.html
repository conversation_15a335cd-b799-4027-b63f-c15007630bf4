management/frontend/leasing.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>租赁管理 - 厦门地铁商业点位管理系统</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }
    .nav-item {
      transition: all 0.3s ease;
    }
    .nav-item:hover {
      transform: translateY(-2px);
    }
    .card {
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    #particles-js {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    .contract-status {
      position: relative;
      padding-left: 1.5rem;
    }
    .contract-status:before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
    }
    .status-active:before {
      background-color: #10B981;
    }
    .status-expiring:before {
      background-color: #F59E0B;
    }
    .status-expired:before {
      background-color: #EF4444;
    }
  </style>
</head>
<body class="antialiased">
  <div id="particles-js"></div>
  
  <header class="bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg">
    <div class="container mx-auto px-4 py-6">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
          </svg>
          <h1 class="text-2xl font-bold">厦门地铁商业点位管理系统</h1>
        </div>
        <nav class="hidden md:flex space-x-8">
          <a href="index.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">首页</a>
          <a href="map.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">点位地图</a>
          <a href="leasing.html" class="nav-item font-medium px-3 py-2 rounded-md bg-blue-700">租赁管理</a>
          <a href="finance.html" class="nav-item font-medium px-3 py-2 rounded-md hover:bg-blue-700">财务统计</a>
        </nav>
        <div class="flex items-center space-x-4">
          <div class="relative">
            <button class="p-2 rounded-full hover:bg-blue-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
            </button>
            <span class="absolute top-0 right-0 h-3 w-3 rounded-full bg-red-500"></span>
          </div>
          <div class="flex items-center space-x-2">
            <img src="https://picsum.photos/200/200?random=1" alt="用户头像" class="h-10 w-10 rounded-full">
            <span class="font-medium">管理员</span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <h2 class="text-3xl font-bold text-gray-800">租赁管理</h2>
      <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        新增合同
      </button>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
      <div class="lg:col-span-2">
        <div class="card bg-white rounded-lg p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">租户列表</h3>
            <div class="flex space-x-2">
              <div class="relative">
                <select class="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>全部租户</option>
                  <option>活跃租户</option>
                  <option>即将到期</option>
                  <option>已到期</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
              <div class="relative">
                <input type="text" placeholder="搜索租户..." class="bg-white border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute right-3 top-2.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">租户名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同状态</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full" src="https://picsum.photos/200/200?random=2" alt="">
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">星巴克咖啡</div>
                        <div class="text-sm text-gray-500">餐饮</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张经理</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">138****5678</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="contract-status status-active">2026-12-31到期</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                    <button class="text-blue-600 hover:text-blue-900">续约</button>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full" src="https://picsum.photos/200/200?random=3" alt="">
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">肯德基</div>
                        <div class="text-sm text-gray-500">快餐</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李店长</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">159****1234</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="contract-status status-expiring">2025-08-15到期</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                    <button class="text-blue-600 hover:text-blue-900">续约</button>
                  </td>
                </tr>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full" src="https://picsum.photos/200/200?random=4" alt="">
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">周黑鸭</div>
                        <div class="text-sm text-gray-500">食品零售</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王经理</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">186****9876</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="contract-status status-expired">2025-07-10到期</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                    <button class="text-blue-600 hover:text-blue-900">续约</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <div>
        <div class="card bg-white rounded-lg p-6 mb-6">
          <h3 class="text-xl font-bold text-gray-800 mb-4">合同状态统计</h3>
          <div class="space-y-4">
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm font-medium text-gray-700">有效合同</span>
                <span class="text-sm font-medium text-gray-700">65%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full" style="width: 65%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm font-medium text-gray-700">即将到期</span>
                <span class="text-sm font-medium text-gray-700">20%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-yellow-500 h-2 rounded-full" style="width: 20%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between mb-1">
                <span class="text-sm font-medium text-gray-700">已到期</span>
                <span class="text-sm font-medium text-gray-700">15%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full" style="width: 15%"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="card bg-white rounded-lg p-6">
          <h3 class="text-xl font-bold text-gray-800 mb-4">租金收入</h3>
          <div class="flex items-end justify-between mb-4">
            <div>
              <p class="text-gray-500">本月预计</p>
              <h3 class="text-2xl font-bold text-blue-600">¥1,280,000</h3>
            </div>
            <div>
              <p class="text-gray-500">已收租金</p>
              <h3 class="text-2xl font-bold text-green-600">¥980,000</h3>
            </div>
          </div>
          <div class="bg-gray-100 rounded-full h-4 mb-2">
            <div class="bg-gradient-to-r from-blue-500 to-green-500 h-4 rounded-full" style="width: 76%"></div>
          </div>
          <p class="text-sm text-gray-500 text-right">76% 已收取</p>
        </div>
      </div>
    </div>

    <div class="card bg-white rounded-lg p-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-bold text-gray-800">合同管理</h3>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition">导出Excel</button>
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition">打印</button>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同编号</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">租户名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">点位编号</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止日期</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">月租金</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XM-HT-2023-045</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">麦当劳</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">XM-2023-045</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-01 至 2025-07-31</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥45,000</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                <button class="text-blue-600 hover:text-blue-900">续约</button>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XM-HT-2023-067</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">必胜客</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">XM-2023-067</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-15 至 2025-08-14</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥38,000</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                <button class="text-blue-600 hover:text-blue-900">续约</button>
              </td>
            </tr>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XM-HT-2023-089</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">真功夫</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">XM-2023-089</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-10-01 至 2025-09-30</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥32,000</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                <button class="text-blue-600 hover:text-blue-900">续约</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </main>

  <footer class="bg-gray-800 text-white py-8 mt-12">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-lg font-bold mb-4">厦门地铁商业点位管理系统</h3>
          <p class="text-gray-400">专业的地铁商业资产管理平台，助力商业运营效率提升。</p>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li><a href="map.html" class="text-gray-400 hover:text-white">点位地图</a></li>
            <li><a href="leasing.html" class="text-gray-400 hover:text-white">租赁管理</a></li>
            <li><a href="finance.html" class="text-gray-400 hover:text-white">财务统计</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">联系我们</h3>
          <p class="text-gray-400">电话：0592-12345678</p>
          <p class="text-gray-400">邮箱：<EMAIL></p>
        </div>
      </div>
      <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
        <p>© 2025 厦门地铁商业管理有限公司. 保留所有权利.</p>
      </div>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
  <script>
    // 粒子效果初始化
    document.addEventListener('DOMContentLoaded', function() {
      particlesJS('particles-js', {
        particles: {
          number: { value: 80, density: { enable: true, value_area: 800 } },
          color: { value: "#3b82f6" },
          shape: { type: "circle" },
          opacity: { value: 0.5, random: true },
          size: { value: 3, random: true },
          line_linked: { enable: true, distance: 150, color: "#3b82f6", opacity: 0.4, width: 1 },
          move: { enable: true, speed: 2, direction: "none", random: true, straight: false, out_mode: "out" }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: { enable: true, mode: "grab" },
            onclick: { enable: true, mode: "push" }
          }
        }
      });
    });
  </script>
  <script src="js/app.js"></script>
</body>
</html>
```
